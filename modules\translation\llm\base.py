from typing import Any
import numpy as np
from abc import abstractmethod
import cv2
import base64

from ..base import LLMTranslation
from ...utils.textblock import TextBlock
from ...utils.translator_utils import get_raw_text, set_texts_from_json


class BaseLLMTranslation(LLMTranslation):
    """Base class for LLM-based translation engines with shared functionality."""

    def __init__(self):
        self.source_lang = None
        self.target_lang = None
        self.api_key = None
        self.api_url = None
        self.model = None
        self.img_as_llm_input = False
        self.temperature = None
        self.top_p = None
        self.max_tokens = None

    def initialize(
        self, config: dict, source_lang: str, target_lang: str, **kwargs
    ) -> None:
        """
        Initialize the LLM translation engine.

        Args:
            config: config object with credentials
            source_lang: Source language name
            target_lang: Target language name
            **kwargs: Engine-specific initialization parameters
        """
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.img_as_llm_input = config.get("image_input_enabled", True)
        self.temperature = config.get("temperature", 1)
        self.top_p = config.get("top_p", 0.95)
        self.max_tokens = config.get("max_tokens", 5000)

    def translate(
        self, blk_list: list[TextBlock], image: np.ndarray, extra_context: str
    ) -> list[TextBlock]:
        """
        Translate text blocks using LLM.

        Args:
            blk_list: List of TextBlock objects to translate
            image: Image as numpy array
            extra_context: Additional context information for translation

        Returns:
            List of updated TextBlock objects with translations
        """
        try:
            entire_raw_text = get_raw_text(blk_list)
            system_prompt = self.get_system_prompt(self.source_lang, self.target_lang)
            user_prompt = f"{extra_context}\nMake the translation sound as natural as possible.\nTranslate this:\n{entire_raw_text}"

            entire_translated_text = self._perform_translation(
                user_prompt, system_prompt, image
            )
            set_texts_from_json(blk_list, entire_translated_text)

        except Exception as e:
            print(f"{type(self).__name__} translation error: {str(e)}")

        return blk_list

    @abstractmethod
    def _perform_translation(
        self, user_prompt: str, system_prompt: str, image: np.ndarray
    ) -> str:
        """
        Perform translation using specific LLM.

        Args:
            user_prompt: User prompt for LLM
            system_prompt: System prompt for LLM
            image: Image as numpy array

        Returns:
            Translated JSON text
        """
        pass

    def encode_image(self, image: np.ndarray, ext=".jpg"):
        """
        Encode CV2/numpy image directly to base64 string using cv2.imencode.

        Args:
            image: Numpy array representing the image
            ext: Extension/format to encode the image as (".png" by default for higher quality)

        Returns:
            Tuple of (Base64 encoded string, mime_type)
        """
        # Direct encoding from numpy/cv2 format to bytes
        success, buffer = cv2.imencode(ext, image)
        if not success:
            raise ValueError(f"Failed to encode image with format {ext}")

        # Convert to base64
        img_str = base64.b64encode(buffer).decode("utf-8")

        # Map extension to mime type
        mime_types = {
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".png": "image/png",
            ".webp": "image/webp",
        }
        mime_type = mime_types.get(ext.lower(), f"image/{ext[1:].lower()}")

        return img_str, mime_type
