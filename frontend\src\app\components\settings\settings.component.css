.settings-container {
  margin: 20px 0;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 10px 0;
  border-bottom: 2px solid var(--comic-accent);
}

.settings-header:hover {
  background: rgba(255, 210, 63, 0.1);
  border-radius: var(--comic-border-radius);
  padding: 10px;
  margin: -10px;
}

.settings-icon {
  margin-right: 10px;
  font-size: 1.5rem;
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.expand-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
  color: var(--comic-primary);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.settings-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.settings-content.expanded {
  max-height: 1000px;
  padding-top: 20px;
}

.setting-item {
  margin-bottom: 25px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--comic-border-radius);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.setting-item:hover {
  border-color: var(--comic-accent);
  background: rgba(255, 255, 255, 0.8);
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.setting-label {
  font-family: "Comic Neue", cursive;
  font-weight: 700;
  color: var(--comic-text-primary);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.setting-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.setting-description {
  font-size: 0.9rem;
  color: var(--comic-text-secondary);
  margin: 8px 0 0 0;
  line-height: 1.4;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 25px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border: 2px solid #000;
  border-radius: 25px;
  transition: 0.3s;
}

.toggle-label:before {
  position: absolute;
  content: "";
  height: 17px;
  width: 17px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: 0.3s;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

input:checked + .toggle-label {
  background-color: var(--comic-success);
}

input:checked + .toggle-label:before {
  transform: translateX(23px);
}

/* Context Input */
.context-input {
  resize: vertical;
  min-height: 80px;
}

/* Slider */
.slider-container {
  margin: 10px 0;
}

.confidence-slider {
  width: 100%;
  height: 8px;
  border-radius: 5px;
  background: var(--comic-bg-secondary);
  outline: none;
  border: 2px solid #000;
  cursor: pointer;
}

.confidence-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--comic-primary);
  border: 2px solid #000;
  cursor: pointer;
  box-shadow: var(--comic-shadow);
}

.confidence-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--comic-primary);
  border: 2px solid #000;
  cursor: pointer;
  box-shadow: var(--comic-shadow);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--comic-text-secondary);
  margin-top: 5px;
}

/* Settings Actions */
.settings-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 2px solid var(--comic-accent);
}

.reset-btn {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .setting-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .toggle-switch {
    align-self: flex-end;
  }
}
