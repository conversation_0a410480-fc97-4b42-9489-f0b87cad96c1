.result-container {
  max-width: 800px;
  margin: 0 auto;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 30px 0;
}

.progress-step {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: var(--comic-border-radius);
  transition: all 0.3s ease;
  position: relative;
}

.step-completed {
  background: rgba(6, 255, 165, 0.1);
  border: 2px solid var(--comic-success);
}

.step-active {
  background: rgba(255, 107, 53, 0.1);
  border: 2px solid var(--comic-primary);
  animation: pulse-border 2s infinite;
}

.step-pending {
  background: rgba(0, 0, 0, 0.05);
  border: 2px solid #ddd;
}

@keyframes pulse-border {
  0% {
    border-color: var(--comic-primary);
  }
  50% {
    border-color: var(--comic-secondary);
  }
  100% {
    border-color: var(--comic-primary);
  }
}

.step-icon {
  font-size: 2rem;
  margin-right: 15px;
  min-width: 50px;
  text-align: center;
}

.step-content {
  flex: 1;
}

.step-label {
  font-family: 'Bangers', cursive;
  font-size: 1.3rem;
  color: var(--comic-text-primary);
  margin-bottom: 5px;
  letter-spacing: 1px;
}

.step-status {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
}

.status-completed {
  color: var(--comic-success);
  font-weight: bold;
}

.status-processing {
  color: var(--comic-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-pending {
  color: var(--comic-text-secondary);
}

.step-connector {
  position: absolute;
  left: 40px;
  bottom: -20px;
  width: 2px;
  height: 20px;
  background: #ddd;
}

.step-completed .step-connector {
  background: var(--comic-success);
}

.error-message {
  background: rgba(255, 0, 110, 0.1);
  border: var(--comic-border);
  border-color: var(--comic-danger);
  border-radius: var(--comic-border-radius);
  padding: 20px;
  margin: 20px 0;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.error-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.error-content h4 {
  margin: 0 0 10px 0;
  color: var(--comic-danger);
  font-family: 'Bangers', cursive;
  font-size: 1.4rem;
}

.error-content p {
  margin: 0 0 15px 0;
  color: var(--comic-text-primary);
}

.blocks-info {
  margin: 30px 0;
}

.blocks-summary {
  display: flex;
  gap: 20px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.8);
  padding: 10px 15px;
  border-radius: var(--comic-border-radius);
  border: 2px solid var(--comic-accent);
}

.summary-icon {
  font-size: 1.2rem;
}

.summary-text {
  font-weight: bold;
  color: var(--comic-text-primary);
}

.blocks-list {
  max-height: 400px;
  overflow-y: auto;
  border: var(--comic-border);
  border-radius: var(--comic-border-radius);
  background: rgba(255, 255, 255, 0.9);
}

.block-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.block-item:last-child {
  border-bottom: none;
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.block-number {
  font-family: 'Bangers', cursive;
  color: var(--comic-primary);
  font-size: 1.1rem;
}

.block-position {
  font-size: 0.8rem;
  color: var(--comic-text-secondary);
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

.block-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.original-text {
  color: var(--comic-text-secondary);
}

.translated-text {
  color: var(--comic-text-primary);
  font-weight: bold;
}

.no-text {
  color: var(--comic-text-secondary);
  font-style: italic;
}

.result-image-container {
  margin: 30px 0;
  text-align: center;
}

.image-wrapper {
  margin: 20px 0;
  border: var(--comic-border);
  border-radius: var(--comic-border-radius);
  overflow: hidden;
  box-shadow: var(--comic-shadow);
  display: inline-block;
  max-width: 100%;
}

.result-image {
  max-width: 100%;
  height: auto;
  display: block;
}

.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px 0;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-animation {
  margin: 20px 0;
}

.comic-loading.large {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

.loading-text {
  font-size: 1.2rem;
  color: var(--comic-text-primary);
  margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .progress-step {
    padding: 10px;
  }
  
  .step-icon {
    font-size: 1.5rem;
    margin-right: 10px;
    min-width: 40px;
  }
  
  .step-label {
    font-size: 1.1rem;
  }
  
  .blocks-summary {
    flex-direction: column;
    gap: 10px;
  }
  
  .summary-item {
    justify-content: center;
  }
  
  .block-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .result-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .result-actions .comic-btn {
    width: 100%;
    max-width: 250px;
  }
}
