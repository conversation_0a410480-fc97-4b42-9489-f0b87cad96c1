.language-selector {
  display: flex;
  align-items: end;
  gap: 15px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.language-group {
  flex: 1;
  min-width: 200px;
}

.comic-label {
  display: block;
  font-family: "Comic Neue", cursive;
  font-weight: 700;
  color: var(--comic-text-primary);
  margin-bottom: 8px;
  font-size: 1rem;
}

.label-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.language-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.language-flag {
  position: absolute;
  left: 12px;
  z-index: 2;
  font-size: 1.2rem;
  pointer-events: none;
}

.language-select {
  padding-left: 45px !important;
  width: 100%;
}

.swap-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.swap-btn {
  background: var(--comic-accent);
  border: var(--comic-border);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--comic-shadow);
}

.swap-btn:hover:not(:disabled) {
  transform: translate(-2px, -2px) rotate(180deg);
  box-shadow: var(--comic-shadow-hover);
  background: var(--comic-secondary);
}

.swap-btn:active:not(:disabled) {
  transform: translate(1px, 1px) rotate(180deg);
  box-shadow: 2px 2px 0px #000000;
}

.swap-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #ccc;
}

.swap-icon {
  font-size: 1.5rem;
  transition: transform 0.2s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .language-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .language-group {
    min-width: unset;
  }
  
  .swap-container {
    order: 3;
    margin: 10px 0;
  }
}
