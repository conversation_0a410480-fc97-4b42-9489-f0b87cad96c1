import { TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { ProcessResponse } from './services/comic-translate.service';

describe('AppComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppComponent],
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have the 'Comic Translate' title`, () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.title).toEqual('Comic Translate');
  });

  it('should render title', () => {
    const fixture = TestBed.createComponent(AppComponent);
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, Comic Translate');
  });

  it('should initialize with default values', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;

    expect(app.currentView).toEqual('upload');
    expect(app.uploadResponse).toBeNull();
    expect(app.sourceLanguage).toEqual('');
    expect(app.targetLanguage).toEqual('');
    expect(app.errorMessage).toEqual('');
  });

  it('should handle image upload success', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    const mockResponse: ProcessResponse = {
      image_id: 'test-image-123',
      blocks: [],
      status: 'uploaded'
    };

    app.onImageUploaded(mockResponse);

    expect(app.uploadResponse).toEqual(mockResponse);
    expect(app.currentView).toEqual('processing');
    expect(app.errorMessage).toEqual('');
  });

  it('should handle upload error', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    const errorMessage = 'Upload failed';

    app.onUploadError(errorMessage);

    expect(app.errorMessage).toEqual(errorMessage);
    expect(app.uploadResponse).toBeNull();
  });

  it('should handle language selection', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    const sourceLanguage = 'en';
    const targetLanguage = 'vi';

    app.onLanguagesSelected(sourceLanguage, targetLanguage);

    expect(app.sourceLanguage).toEqual(sourceLanguage);
    expect(app.targetLanguage).toEqual(targetLanguage);
  });

  it('should reset to upload view', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;

    // Set some values first
    app.currentView = 'processing';
    app.uploadResponse = { image_id: 'test-123', blocks: [], status: 'processing' };
    app.errorMessage = 'some error';

    app.resetToUpload();

    expect(app.currentView).toEqual('upload');
    expect(app.uploadResponse).toBeNull();
    expect(app.errorMessage).toEqual('');
  });
});
